body {
  font-family: Arial, sans-serif;
  background: linear-gradient(to right, #ffecd2, #fcb69f);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin: 0;
}

.login-container {
  background: white;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  position: relative;
}

input {
  width: 80%;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #ccc;
  border-radius: 5px;
}

button {
  padding: 10px 25px;
  border: none;
  background-color: #3498db;
  color: white;
  font-size: 16px;
  border-radius: 5px;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
}

/* Shake animation for wrong credentials */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

.login-container.shake {
  animation: shake 0.6s ease-in-out;
}

/* Smooth left-right movement when credentials are wrong */
@keyframes smoothMove {
  0% { transform: translateX(0); }
  50% { transform: translateX(20px); }
  100% { transform: translateX(-20px); }
}

.login-container.wrong-credentials:hover {
  animation: smoothMove 1s ease-in-out infinite alternate;
}

.login-container.correct-credentials {
  animation: none;
}

.login-container.correct-credentials:hover {
  animation: none;
}

#message {
  color: red;
  margin-top: 10px;
}
