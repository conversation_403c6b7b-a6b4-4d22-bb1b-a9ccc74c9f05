const loginBtn = document.getElementById('loginBtn');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const message = document.getElementById('message');

let moveInterval;
let position = 0;
let direction = 1;

function startButtonMovement() {
  moveInterval = setInterval(() => {
    position += direction * 5;
    if (position > 150 || position < -150) direction *= -1;
    loginBtn.style.transform = `translateX(${position}px)`;
  }, 30);
}

function stopButtonMovement() {
  clearInterval(moveInterval);
  loginBtn.style.transform = 'translateX(0)';
}

function checkCredentialsLive() {
  const username = usernameInput.value.trim();
  const password = passwordInput.value.trim();

  if (username === 'admin' && password === '1234') {
    stopButtonMovement();
    message.style.color = 'green';
    message.textContent = 'Credentials correct. You may login.';
  } else {
    if (!moveInterval) startButtonMovement();
    message.style.color = 'red';
    message.textContent = 'Enter correct credentials.';
  }
}

function submitLogin() {
  const username = usernameInput.value.trim();
  const password = passwordInput.value.trim();

  if (username === 'admin' && password === '1234') {
    message.style.color = 'green';
    message.textContent = 'Login successful!';
  } else {
    message.style.color = 'red';
    message.textContent = 'Login failed! Wrong credentials.';
  }
}

// Start movement on page load
startButtonMovement();

// Add live check on input
usernameInput.addEventListener('input', checkCredentialsLive);
passwordInput.addEventListener('input', checkCredentialsLive);
